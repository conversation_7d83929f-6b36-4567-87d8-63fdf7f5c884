﻿import express from 'express';
import { createCustomer, getCustomers, getCustomerById, updateCustomer, deactivateCustomer  } from '../controllers/customerController';
import { authenticateToken, authorizeRoles  } from '../middleware/authMiddleware';

const router = express.Router();

// All routes require authentication and employee role
router.use(authenticateToken, authorizeRoles('admin', 'manager', 'teller'));

// Create customer
router.post('/', createCustomer);

// Get all customers
router.get('/', getCustomers);

// Get customer by ID
router.get('/:id', getCustomerById);

// Update customer
router.put('/:id', updateCustomer);

// Deactivate (soft delete) customer
router.delete('/:id', deactivateCustomer);

export default router; 
