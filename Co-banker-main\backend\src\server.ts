import 'dotenv/config';
import express, { Application } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import path from 'path';

// Handle uncaught exceptions
process.on('uncaughtException', (error: Error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Import middleware
import { errorHandler, notFound } from './middleware/errorMiddleware';
import { logger } from './utils/logger';
import { authenticateToken } from './middleware/authMiddleware';
import { validateSystemHealth, healthCheck } from './config/validation';

// Import routes
import accountRoutes from './routes/accountRoutes';
import customerRoutes from './routes/customerRoutes';
import transactionRoutes from './routes/transactionRoutes';
import authRoutes from './routes/authRoutes';
import memberRoutes from './routes/memberRoutes';
import loanRoutes from './routes/loanRoutes';
import repaymentRoutes from './routes/repaymentRoutes';
import recurringDepositRoutes from './routes/recurringDepositRoutes';
import fixedDepositRoutes from './routes/fixedDepositRoutes';
import shareRoutes from './routes/shareRoutes';
import dividendRoutes from './routes/dividendRoutes';

const app: Application = express();
const PORT: number = parseInt(process.env.PORT || '3001');

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // allow 50 requests per 15 minutes, then...
  delayMs: () => 500, // begin adding 500ms of delay per request above 50 (new recommended config)
});

// Middleware
app.use(compression());
app.use(limiter);
app.use(speedLimiter);
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://yourdomain.com'] 
    : ['http://localhost:3000', 'http://localhost:19006'],
  credentials: true,
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Health check
app.get('/health', async (req, res) => {
  try {
    const health = await healthCheck();
    const statusCode = health.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(health);
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

// API Routes
app.use(`/api/${process.env.API_VERSION || 'v1'}/auth`, authRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/accounts`, authenticateToken, accountRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/customers`, authenticateToken, customerRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/transactions`, authenticateToken, transactionRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/members`, authenticateToken, memberRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/loans`, authenticateToken, loanRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/repayments`, authenticateToken, repaymentRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/recurring-deposits`, authenticateToken, recurringDepositRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/fixed-deposits`, authenticateToken, fixedDepositRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/shares`, authenticateToken, shareRoutes);
app.use(`/api/${process.env.API_VERSION || 'v1'}/dividends`, authenticateToken, dividendRoutes);

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server with system validation
const startServer = async () => {
  try {
    // Validate system health before starting
    await validateSystemHealth();

    app.listen(PORT, () => {
      logger.info(`CoBanker Backend Server running on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV}`);
      logger.info(`Health check: http://localhost:${PORT}/health`);
      logger.info('Server started successfully');
    });
  } catch (error) {
    logger.error('Failed to start server:', error.message);
    process.exit(1);
  }
};

// Start the server
startServer();

module.exports = app; 