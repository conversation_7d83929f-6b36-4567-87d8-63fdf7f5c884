# Test Environment Variables for COBANKER Backend

# JWT Configuration
JWT_SECRET=test_jwt_secret_key_for_cobanker_testing_12345
JWT_EXPIRES_IN=24h

# Supabase Configuration (Test/Mock values)
SUPABASE_URL=https://test.supabase.co
SUPABASE_ANON_KEY=test_anon_key_12345
SUPABASE_SERVICE_ROLE_KEY=test_service_role_key_12345

# Server Configuration
PORT=3001
NODE_ENV=test
API_VERSION=v1

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cobanker_test
DB_USER=test_user
DB_PASSWORD=test_password

# Email Configuration (Test)
EMAIL_HOST=smtp.test.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=test_password

# SMS Configuration (Test)
SMS_API_KEY=test_sms_api_key
SMS_SENDER_ID=COBANKER

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH=./uploads

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/test.log

# Security Configuration
BCRYPT_ROUNDS=10
SESSION_SECRET=test_session_secret_key

# Banking Configuration
MINIMUM_BALANCE=500
MAXIMUM_DAILY_TRANSACTION=100000
INTEREST_CALCULATION_METHOD=compound

# Notification Configuration
ENABLE_EMAIL_NOTIFICATIONS=false
ENABLE_SMS_NOTIFICATIONS=false
ENABLE_PUSH_NOTIFICATIONS=false
