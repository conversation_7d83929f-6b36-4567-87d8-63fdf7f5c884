import express from 'express';
import { createMember, getMembers, getMemberById, updateMember } from '../controllers/memberController';
import { authenticateToken, authorizeRoles } from '../middleware/authMiddleware';

const router = express.Router();

// All routes require authentication and employee role
router.use(authenticateToken, authorizeRoles('admin', 'manager', 'teller'));

// Create member
router.post('/', createMember);

// Get all members
router.get('/', getMembers);

// Get member by ID
router.get('/:id', getMemberById);

// Update member
router.put('/:id', updateMember);

export default router;