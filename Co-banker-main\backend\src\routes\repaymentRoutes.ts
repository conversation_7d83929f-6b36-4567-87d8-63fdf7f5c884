﻿import express from 'express';
import { createRepayment, getRepaymentsByLoan  } from '../controllers/repaymentController';
import { authenticateToken, authorizeRoles  } from '../middleware/authMiddleware';

const router = express.Router();

// All routes require authentication and teller/manager/admin role
router.use(authenticateToken, authorizeRoles('admin', 'manager', 'teller'));

// Record a repayment
router.post('/', createRepayment);

// Get all repayments for a loan
router.get('/loan/:loan_id', getRepaymentsByLoan);

export default router; 
