{"name": "cobanker-backend", "version": "1.0.0", "description": "CoBanker - Cooperative Banking System Backend", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "start:ts": "ts-node src/server.ts", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "dev:js": "nodemon src/server.js", "build": "tsc", "build:watch": "tsc --watch", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage --watchAll=false", "test:ci": "jest --coverage --watchAll=false --testTimeout=30000 --passWithNoTests", "test:models": "jest src/tests/ --testNamePattern=\"Model|model\"", "test:api": "jest src/tests/ --testNamePattern=\"api|endpoint|controller\"", "test:auth": "jest src/tests/ --testNamePattern=\"auth|Auth\"", "test:banking": "jest src/tests/ --testNamePattern=\"account|transaction|loan|deposit\"", "validate:syntax": "npm run type-check", "validate:env": "ts-node -e \"import('./src/config/validation').then(v => v.validateProductionConfig())\"", "validate:health": "ts-node -e \"import('./src/config/validation').then(v => v.healthCheck().then(h => console.log('Health:', h.status)))\"", "validate:all": "npm run validate:syntax && npm run validate:env && npm run test:ci", "migrate": "ts-node src/database/migrate.ts", "migrate:js": "node src/database/migrate.js", "seed": "ts-node src/database/seed.ts", "seed:js": "node src/database/seed.js", "lint": "eslint src/ --ext .ts,.js", "lint:fix": "eslint src/ --ext .ts,.js --fix"}, "keywords": ["banking", "cooperative", "fintech", "nodejs", "express"], "author": "CoBanker Team", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.50.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.0.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "sharp": "^0.33.0", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/joi": "^17.2.2", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.20", "@types/multer": "^2.0.0", "@types/node": "^20.10.0", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}