{"name": "cobanker-backend", "version": "1.0.0", "description": "CoBanker - Cooperative Banking System Backend", "main": "src/server.js", "scripts": {"start": "node dist/server.js", "start:js": "node src/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "dev:js": "nodemon src/server.js", "build": "tsc", "build:watch": "tsc --watch", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage --watchAll=false", "test:ci": "jest --coverage --watchAll=false --testTimeout=30000 --passWithNoTests", "test:models": "jest src/tests/ --testNamePattern=\"Model|model\"", "test:api": "jest src/tests/ --testNamePattern=\"api|endpoint|controller\"", "test:auth": "jest src/tests/ --testNamePattern=\"auth|Auth\"", "test:banking": "jest src/tests/ --testNamePattern=\"account|transaction|loan|deposit\"", "validate:syntax": "npm run type-check", "validate:syntax:js": "node -c src/server.js", "validate:env": "ts-node -e \"require('./src/config/validation').validateProductionConfig()\"", "validate:health": "ts-node -e \"require('./src/config/validation').healthCheck().then(h => console.log('Health:', h.status))\"", "validate:all": "npm run validate:syntax && npm run validate:env && npm run test:ci", "migrate": "ts-node src/database/migrate.ts", "migrate:js": "node src/database/migrate.js", "seed": "ts-node src/database/seed.ts", "seed:js": "node src/database/seed.js"}, "keywords": ["banking", "cooperative", "fintech", "nodejs", "express"], "author": "CoBanker Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "sharp": "^0.33.0", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "winston": "^3.11.0", "dotenv": "^16.3.1", "@supabase/supabase-js": "^2.50.0", "joi": "^17.11.0", "node-cron": "^3.0.3", "compression": "^1.7.4", "express-slow-down": "^2.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/joi": "^17.2.3", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "typescript": "^5.3.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0"}, "engines": {"node": ">=18.0.0"}}