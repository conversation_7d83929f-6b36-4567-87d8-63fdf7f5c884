# COBANKER Backend Production Environment Configuration
# Copy this file to .env and fill in the actual values

# =============================================================================
# REQUIRED ENVIRONMENT VARIABLES
# =============================================================================

# Node Environment
NODE_ENV=production

# Server Configuration
PORT=3000

# JWT Configuration (CRITICAL: Use a strong secret in production)
JWT_SECRET=your_super_secure_jwt_secret_key_at_least_32_characters_long
JWT_EXPIRES_IN=24h

# Supabase Configuration (CRITICAL: Get these from your Supabase project)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# =============================================================================
# OPTIONAL BUT RECOMMENDED ENVIRONMENT VARIABLES
# =============================================================================

# API Configuration
API_VERSION=v1

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/production.log

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_key

# CORS Configuration
CORS_ORIGIN=https://your-frontend-domain.com
CORS_CREDENTIALS=true

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH=./uploads

# Database Configuration (if using additional databases)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cobanker_production
DB_USER=cobanker_user
DB_PASSWORD=secure_database_password

# Email Configuration (for notifications)
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_PORT=587
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=<EMAIL>

# SMS Configuration (for notifications)
SMS_API_KEY=your_sms_api_key
SMS_SENDER_ID=COBANKER
SMS_PROVIDER=twilio

# Banking Configuration
MINIMUM_BALANCE=500
MAXIMUM_DAILY_TRANSACTION=100000
INTEREST_CALCULATION_METHOD=compound
DEFAULT_CURRENCY=INR

# Notification Configuration
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=true
ENABLE_PUSH_NOTIFICATIONS=false

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Cache Configuration (if using Redis)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# External API Configuration
PAYMENT_GATEWAY_URL=https://api.payment-provider.com
PAYMENT_GATEWAY_KEY=your_payment_gateway_key
PAYMENT_GATEWAY_SECRET=your_payment_gateway_secret

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# =============================================================================
# PRODUCTION SECURITY CHECKLIST
# =============================================================================

# 1. JWT_SECRET must be at least 32 characters long and cryptographically secure
# 2. All database credentials must be unique and strong
# 3. CORS_ORIGIN should be set to your actual frontend domain
# 4. All API keys should be kept secure and rotated regularly
# 5. Enable HTTPS in production (handled by reverse proxy/load balancer)
# 6. Set up proper firewall rules
# 7. Enable database SSL connections
# 8. Set up monitoring and alerting
# 9. Configure log rotation
# 10. Set up automated backups

# =============================================================================
# DEPLOYMENT NOTES
# =============================================================================

# 1. Never commit this file with actual secrets to version control
# 2. Use environment variable injection in your deployment platform
# 3. Consider using a secrets management service (AWS Secrets Manager, etc.)
# 4. Set up health checks using the /health endpoint
# 5. Configure load balancing if running multiple instances
# 6. Set up SSL termination at the load balancer level
# 7. Configure proper logging aggregation
# 8. Set up monitoring dashboards
# 9. Configure automated scaling based on metrics
# 10. Set up disaster recovery procedures
