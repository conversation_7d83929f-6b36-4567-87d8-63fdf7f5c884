{
  "compilerOptions": {
    /* Language and Environment */
    "target": "ES2020",
    "lib": ["ES2020"],
    "module": "CommonJS",
    "moduleResolution": "node",

    /* Emit */
    "outDir": "./dist",
    "rootDir": "./src",
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "sourceMap": true,
    "declaration": true,
    "declarationMap": true,

    /* Interop Constraints */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,

    /* Type Checking */
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "exactOptionalPropertyTypes": true,

    /* Completeness */
    "skipLibCheck": true,
    "resolveJsonModule": true,

    /* Banking Application Specific */
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitThis": true,
    "alwaysStrict": true,

    /* Path Mapping for Banking Modules */
    "baseUrl": "./src",
    "paths": {
      "@/config/*": ["config/*"],
      "@/controllers/*": ["controllers/*"],
      "@/models/*": ["models/*"],
      "@/routes/*": ["routes/*"],
      "@/middleware/*": ["middleware/*"],
      "@/validators/*": ["validators/*"],
      "@/utils/*": ["utils/*"],
      "@/types/*": ["types/*"],
      "@/tests/*": ["tests/*"]
    }
  },
  "include": [
    "src/**/*",
    "*.ts",
    "*.js"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "coverage",
    "logs",
    "uploads",
    "**/*.test.js",
    "**/*.spec.js"
  ],
  "ts-node": {
    "esm": false,
    "experimentalSpecifierResolution": "node"
  }
}
