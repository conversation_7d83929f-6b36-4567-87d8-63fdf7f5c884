# PowerShell script to convert JavaScript files to TypeScript
# This script will rename .js files to .ts and update import/export statements

Write-Host "Starting TypeScript conversion for COBANKER backend..." -ForegroundColor Green

# Get all JavaScript files in src directory
$jsFiles = Get-ChildItem -Path "src" -Filter "*.js" -Recurse

Write-Host "Found $($jsFiles.Count) JavaScript files to convert" -ForegroundColor Yellow

foreach ($file in $jsFiles) {
    $oldPath = $file.FullName
    $newPath = $oldPath -replace '\.js$', '.ts'
    
    Write-Host "Converting: $($file.Name)" -ForegroundColor Cyan
    
    # Read file content
    $content = Get-Content -Path $oldPath -Raw
    
    # Basic TypeScript conversions
    # Convert require statements to import statements
    $content = $content -replace "const\s+\{\s*([^}]+)\s*\}\s*=\s*require\('([^']+)'\);?", "import { `$1 } from '`$2';"
    $content = $content -replace "const\s+(\w+)\s*=\s*require\('([^']+)'\);?", "import `$1 from '`$2';"
    
    # Convert module.exports to export
    $content = $content -replace "module\.exports\s*=\s*\{([^}]+)\};?", "export { `$1 };"
    $content = $content -replace "module\.exports\s*=\s*(\w+);?", "export default `$1;"
    
    # Update file extensions in import statements
    $content = $content -replace "from\s+'\.\.?/[^']*\.js'", { $_.Value -replace '\.js', '.ts' }
    $content = $content -replace "from\s+'\.\.?/[^']*(?<!\.ts)'", { $_.Value + '.ts' }
    
    # Write updated content to new TypeScript file
    Set-Content -Path $newPath -Value $content -Encoding UTF8
    
    # Remove old JavaScript file
    Remove-Item -Path $oldPath -Force
    
    Write-Host "  ✓ Converted to: $([System.IO.Path]::GetFileName($newPath))" -ForegroundColor Green
}

Write-Host "`nTypeScript conversion completed!" -ForegroundColor Green
Write-Host "Converted $($jsFiles.Count) files from .js to .ts" -ForegroundColor Yellow

# Update package.json main entry if it exists
$packageJsonPath = "package.json"
if (Test-Path $packageJsonPath) {
    $packageJson = Get-Content -Path $packageJsonPath -Raw | ConvertFrom-Json
    if ($packageJson.main -eq "src/server.js") {
        $packageJson.main = "dist/server.js"
        $packageJson | ConvertTo-Json -Depth 10 | Set-Content -Path $packageJsonPath -Encoding UTF8
        Write-Host "Updated package.json main entry to dist/server.js" -ForegroundColor Green
    }
}

Write-Host "`nNext steps:" -ForegroundColor Magenta
Write-Host "1. Run 'npm run type-check' to verify TypeScript compilation" -ForegroundColor White
Write-Host "2. Run 'npm run build' to compile TypeScript to JavaScript" -ForegroundColor White
Write-Host "3. Run 'npm test' to ensure all tests pass" -ForegroundColor White
Write-Host "4. Commit changes to the backend-clean branch" -ForegroundColor White
