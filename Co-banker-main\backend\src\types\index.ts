// Common types for COBANKER Banking Application

import { Request } from 'express';

// User and Authentication Types
export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  role: UserRole;
  status: UserStatus;
  bank_id?: string;
  branch_id?: string;
  created_at: Date;
  updated_at: Date;
}

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  MANAGER = 'manager',
  CASHIER = 'cashier',
  LOAN_OFFICER = 'loan_officer',
  ACCOUNTANT = 'accountant',
  AUDITOR = 'auditor',
  CUSTOMER_SERVICE = 'customer_service',
  MEMBER = 'member',
  CUSTOMER = 'customer'
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending'
}

// Account Types
export interface Account {
  id: string;
  account_number: string;
  customer_id: string;
  account_type: AccountType;
  balance: number;
  status: AccountStatus;
  bank_id: string;
  branch_id: string;
  created_at: Date;
  updated_at: Date;
}

export enum AccountType {
  SAVINGS = 'savings',
  CURRENT = 'current',
  FIXED_DEPOSIT = 'fixed_deposit',
  RECURRING_DEPOSIT = 'recurring_deposit'
}

export enum AccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  CLOSED = 'closed',
  FROZEN = 'frozen'
}

// Transaction Types
export interface Transaction {
  id: string;
  transaction_number: string;
  account_id: string;
  transaction_type: TransactionType;
  amount: number;
  balance_after: number;
  description?: string;
  reference_number?: string;
  status: TransactionStatus;
  created_at: Date;
}

export enum TransactionType {
  DEPOSIT = 'deposit',
  WITHDRAWAL = 'withdrawal',
  TRANSFER = 'transfer',
  INTEREST = 'interest',
  FEE = 'fee',
  LOAN_DISBURSEMENT = 'loan_disbursement',
  LOAN_REPAYMENT = 'loan_repayment'
}

export enum TransactionStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// Loan Types
export interface Loan {
  id: string;
  loan_number: string;
  customer_id: string;
  loan_type: LoanType;
  principal_amount: number;
  interest_rate: number;
  tenure_months: number;
  monthly_emi: number;
  outstanding_amount: number;
  status: LoanStatus;
  bank_id: string;
  branch_id: string;
  created_at: Date;
  updated_at: Date;
}

export enum LoanType {
  PERSONAL = 'personal',
  HOME = 'home',
  VEHICLE = 'vehicle',
  BUSINESS = 'business',
  EDUCATION = 'education',
  GOLD = 'gold'
}

export enum LoanStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  ACTIVE = 'active',
  CLOSED = 'closed',
  DEFAULTED = 'defaulted'
}

// Bank and Branch Types
export interface Bank {
  id: string;
  name: string;
  code: string;
  bank_type: BankType;
  registration_number: string;
  license_number: string;
  address: Address;
  contact_info: ContactInfo;
  status: BankStatus;
  created_at: Date;
  updated_at: Date;
}

export interface Branch {
  id: string;
  bank_id: string;
  name: string;
  code: string;
  address: Address;
  contact_info: ContactInfo;
  status: BranchStatus;
  created_at: Date;
  updated_at: Date;
}

export enum BankType {
  COOPERATIVE = 'cooperative',
  COMMERCIAL = 'commercial',
  RURAL = 'rural'
}

export enum BankStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

export enum BranchStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  CLOSED = 'closed'
}

// Common Types
export interface Address {
  street: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
}

export interface ContactInfo {
  phone: string;
  email: string;
  website?: string;
}

// Request Types
export interface AuthenticatedRequest extends Request {
  user?: User;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  errors?: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
}

// Pagination Types
export interface PaginationQuery {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Database Types
export interface DatabaseConfig {
  url: string;
  key: string;
}

// JWT Types
export interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  bankId?: string;
  branchId?: string;
}
