// COBANKER Banking Application Type Definitions

import { Request } from 'express';

// =============================================================================
// AUTHENTICATION & USER TYPES
// =============================================================================

export interface AuthenticatedUser {
  id: string;
  email: string;
  name: string;
  phone: string;
  role: UserRole;
  bank_id: string;
  branch_id?: string;
  employee_id?: string;
  department?: string;
  designation?: string;
  permissions: string[];
  status: UserStatus;
}

export interface AuthenticatedRequest extends Request {
  user: AuthenticatedUser;
}

export type UserRole = 
  | 'admin' 
  | 'manager' 
  | 'cashier' 
  | 'loan_officer' 
  | 'accountant' 
  | 'board_member' 
  | 'member';

export type UserStatus = 
  | 'active' 
  | 'inactive' 
  | 'suspended' 
  | 'pending_approval';

// =============================================================================
// BANKING ACCOUNT TYPES
// =============================================================================

export type AccountType = 
  | 'savings' 
  | 'current' 
  | 'fixed_deposit' 
  | 'recurring_deposit' 
  | 'loan' 
  | 'demat';

export type AccountStatus = 
  | 'active' 
  | 'inactive' 
  | 'closed' 
  | 'frozen' 
  | 'dormant';

export interface BankAccount {
  id: string;
  account_number: string;
  customer_id: string;
  account_type: AccountType;
  balance: number;
  available_balance: number;
  status: AccountStatus;
  bank_id: string;
  branch_id: string;
  opening_date: string;
  closing_date?: string;
  minimum_balance: number;
  interest_rate: number;
  created_at: string;
  updated_at: string;
}

// =============================================================================
// TRANSACTION TYPES
// =============================================================================

export type TransactionType = 
  | 'deposit' 
  | 'withdrawal' 
  | 'transfer' 
  | 'loan_disbursement' 
  | 'loan_repayment' 
  | 'interest_credit' 
  | 'fee_debit' 
  | 'dividend_credit';

export type TransactionStatus = 
  | 'pending' 
  | 'completed' 
  | 'failed' 
  | 'cancelled' 
  | 'reversed';

export interface Transaction {
  id: string;
  transaction_number: string;
  account_id: string;
  transaction_type: TransactionType;
  amount: number;
  balance_before: number;
  balance_after: number;
  description: string;
  reference_number?: string;
  status: TransactionStatus;
  processed_by: string;
  processed_at: string;
  created_at: string;
  updated_at: string;
}

// =============================================================================
// LOAN TYPES
// =============================================================================

export type LoanType = 
  | 'personal' 
  | 'home' 
  | 'vehicle' 
  | 'education' 
  | 'business' 
  | 'agriculture' 
  | 'gold';

export type LoanStatus = 
  | 'applied' 
  | 'under_review' 
  | 'approved' 
  | 'rejected' 
  | 'disbursed' 
  | 'closed' 
  | 'defaulted';

export type RepaymentStatus = 
  | 'ongoing' 
  | 'completed' 
  | 'overdue' 
  | 'defaulted';

export interface Loan {
  id: string;
  loan_number: string;
  customer_id: string;
  loan_type: LoanType;
  principal_amount: number;
  interest_rate: number;
  tenure_months: number;
  emi_amount: number;
  outstanding_amount: number;
  status: LoanStatus;
  repayment_status: RepaymentStatus;
  application_date: string;
  approval_date?: string;
  disbursement_date?: string;
  maturity_date?: string;
  created_at: string;
  updated_at: string;
}

// =============================================================================
// DEPOSIT TYPES
// =============================================================================

export type DepositStatus = 
  | 'active' 
  | 'matured' 
  | 'premature_closure' 
  | 'cancelled';

export interface FixedDeposit {
  id: string;
  fd_number: string;
  customer_id: string;
  principal_amount: number;
  interest_rate: number;
  tenure_months: number;
  maturity_amount: number;
  status: DepositStatus;
  opening_date: string;
  maturity_date: string;
  closure_date?: string;
  created_at: string;
  updated_at: string;
}

export interface RecurringDeposit {
  id: string;
  rd_number: string;
  customer_id: string;
  monthly_amount: number;
  interest_rate: number;
  tenure_months: number;
  maturity_amount: number;
  status: DepositStatus;
  opening_date: string;
  maturity_date: string;
  last_installment_date?: string;
  created_at: string;
  updated_at: string;
}

// =============================================================================
// SHARE & DIVIDEND TYPES
// =============================================================================

export type ShareStatus = 
  | 'active' 
  | 'transferred' 
  | 'surrendered' 
  | 'frozen';

export interface Share {
  id: string;
  share_number: string;
  member_id: string;
  share_value: number;
  quantity: number;
  total_value: number;
  status: ShareStatus;
  purchase_date: string;
  certificate_number: string;
  created_at: string;
  updated_at: string;
}

export type DividendType = 
  | 'annual' 
  | 'interim' 
  | 'special';

export type DividendStatus = 
  | 'declared' 
  | 'approved' 
  | 'distributed' 
  | 'cancelled';

export interface Dividend {
  id: string;
  dividend_year: number;
  dividend_type: DividendType;
  dividend_rate: number;
  total_dividend_amount: number;
  status: DividendStatus;
  record_date: string;
  payment_date: string;
  declaration_date: string;
  created_at: string;
  updated_at: string;
}

// =============================================================================
// BANK & BRANCH TYPES
// =============================================================================

export interface Bank {
  id: string;
  name: string;
  code: string;
  ifsc_code: string;
  address: string;
  phone: string;
  email: string;
  license_number: string;
  established_date: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface Branch {
  id: string;
  bank_id: string;
  name: string;
  code: string;
  ifsc_code: string;
  address: string;
  phone: string;
  email: string;
  manager_id?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

// =============================================================================
// CUSTOMER TYPES
// =============================================================================

export type CustomerStatus = 
  | 'active' 
  | 'inactive' 
  | 'suspended' 
  | 'deceased';

export interface Customer {
  id: string;
  customer_number: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  date_of_birth: string;
  aadhar_number: string;
  pan_number: string;
  status: CustomerStatus;
  kyc_status: 'pending' | 'verified' | 'rejected';
  bank_id: string;
  branch_id: string;
  created_at: string;
  updated_at: string;
}

// =============================================================================
// API RESPONSE TYPES
// =============================================================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface ValidationError {
  field: string;
  message: string;
}

// =============================================================================
// DATABASE TYPES
// =============================================================================

export interface DatabaseConfig {
  url: string;
  anon_key: string;
  service_role_key?: string;
}

export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  memory: NodeJS.MemoryUsage;
  environment: string;
  version: string;
  database?: {
    connected: boolean;
    response_time?: number;
  };
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
export type ID = string;
export type Timestamp = string;
export type Currency = number;
export type Percentage = number;
