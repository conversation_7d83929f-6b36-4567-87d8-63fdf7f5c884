# PowerShell script to convert JavaScript files to TypeScript
# This script will systematically convert all .js files to .ts files with proper imports

Write-Host "Starting JavaScript to TypeScript conversion for COBANKER Backend..." -ForegroundColor Green

# Define the source directory
$srcDir = ".\src"

# Get all JavaScript files recursively
$jsFiles = Get-ChildItem -Path $srcDir -Filter "*.js" -Recurse

Write-Host "Found $($jsFiles.Count) JavaScript files to convert" -ForegroundColor Yellow

foreach ($file in $jsFiles) {
    Write-Host "Converting: $($file.FullName)" -ForegroundColor Cyan
    
    # Read the file content
    $content = Get-Content -Path $file.FullName -Raw
    
    # Convert require statements to import statements
    $content = $content -replace "const\s+\{\s*([^}]+)\s*\}\s*=\s*require\('([^']+)'\);?", "import { `$1 } from '`$2';"
    $content = $content -replace "const\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*require\('([^']+)'\);?", "import `$1 from '`$2';"
    $content = $content -replace "require\('([^']+)'\)", "import('`$1')"
    
    # Convert module.exports to export
    $content = $content -replace "module\.exports\s*=\s*\{([^}]+)\};?", "export { `$1 };"
    $content = $content -replace "module\.exports\s*=\s*([^;]+);?", "export default `$1;"
    
    # Add basic type annotations for common patterns
    $content = $content -replace "const\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*async\s*\(([^)]*)\)\s*=>", "const `$1 = async (`$2): Promise<any> =>"
    $content = $content -replace "const\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*\(([^)]*)\)\s*=>", "const `$1 = (`$2): any =>"
    
    # Add error type annotations
    $content = $content -replace "catch\s*\(\s*error\s*\)", "catch (error: any)"
    $content = $content -replace "catch\s*\(\s*err\s*\)", "catch (err: any)"
    
    # Create the TypeScript file path
    $tsFilePath = $file.FullName -replace "\.js$", ".ts"
    
    # Write the converted content to the TypeScript file
    Set-Content -Path $tsFilePath -Value $content -Encoding UTF8
    
    # Remove the original JavaScript file
    Remove-Item -Path $file.FullName -Force
    
    Write-Host "Converted: $($file.Name) -> $([System.IO.Path]::GetFileName($tsFilePath))" -ForegroundColor Green
}

Write-Host "Conversion completed! Converted $($jsFiles.Count) files." -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Review the converted files for any type errors" -ForegroundColor White
Write-Host "2. Run 'npm run build' to check for TypeScript compilation errors" -ForegroundColor White
Write-Host "3. Add specific type annotations where needed" -ForegroundColor White
Write-Host "4. Update imports to use proper TypeScript types" -ForegroundColor White
