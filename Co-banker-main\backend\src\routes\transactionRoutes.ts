﻿import express from 'express';
import { createTransaction, getTransactions, getTransactionById  } from '../controllers/transactionController';
import { authenticateToken, authorizeRoles  } from '../middleware/authMiddleware';

const router = express.Router();

// All routes require authentication and employee role
router.use(authenticateToken, authorizeRoles('admin', 'manager', 'teller'));

// Create transaction
router.post('/', createTransaction);

// Get all transactions
router.get('/', getTransactions);

// Get transaction by ID
router.get('/:id', getTransactionById);

export default router; 
