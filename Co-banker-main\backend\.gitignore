# COBANKER Backend - Git Ignore File

# =============================================================================
# NODE.JS DEPENDENCIES
# =============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# =============================================================================
# ENVIRONMENT VARIABLES
# =============================================================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# =============================================================================
# LOGS
# =============================================================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# =============================================================================
# TEST COVERAGE REPORTS (HTML/CSS FILES)
# =============================================================================
coverage/
*.lcov
.nyc_output

# =============================================================================
# JEST CACHE
# =============================================================================
.jest/

# =============================================================================
# RUNTIME DATA
# =============================================================================
pids
*.pid
*.seed
*.pid.lock

# =============================================================================
# UPLOADS AND TEMPORARY FILES
# =============================================================================
uploads/
temp/
tmp/
*.tmp

# =============================================================================
# DATABASE FILES (if using local SQLite)
# =============================================================================
*.db
*.sqlite
*.sqlite3

# =============================================================================
# IDE AND EDITOR FILES
# =============================================================================
.vscode/
.idea/
*.swp
*.swo
*~

# =============================================================================
# OS GENERATED FILES
# =============================================================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# =============================================================================
# BUILD OUTPUTS
# =============================================================================
dist/
build/
out/

# =============================================================================
# CI/CD AND GITHUB ACTIONS
# =============================================================================
# Keep .github/ folder for CI/CD workflows
!.github/

# =============================================================================
# SECURITY AND CERTIFICATES
# =============================================================================
*.pem
*.key
*.crt
*.csr

# =============================================================================
# BACKUP FILES
# =============================================================================
*.backup
*.bak
*.old

# =============================================================================
# DOCUMENTATION BUILDS
# =============================================================================
docs/_build/

# =============================================================================
# MONITORING AND PROFILING
# =============================================================================
*.heapsnapshot
*.cpuprofile
