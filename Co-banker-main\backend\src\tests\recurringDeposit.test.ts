const request = require('supertest');
const app = require('../server');

// Test user credentials (ensure this user exists and has correct role)
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword',
};

let AUTH_TOKEN = '';
let TEST_MEMBER_ID = '';
let TEST_ACCOUNT_ID = '';
let RD_ID = '';
let PAYMENT_ID = '';

const authHeader = () => ({ Authorization: `Bearer ${AUTH_TOKEN}` });

describe('Recurring Deposit Module', () => {
  beforeAll(async () => {
    // Login and get token
    const loginRes = await request(app)
      .post('/api/v1/auth/login')
      .send(TEST_USER);
    AUTH_TOKEN = loginRes.body.token || loginRes.body.data?.token;
    expect(AUTH_TOKEN).toBeTruthy();

    // Create customer
    const customerRes = await request(app)
      .post('/api/v1/customers')
      .set(authHeader())
      .send({
        name: 'RD Test Customer',
        email: `rdtest_${Date.now()}@example.com`,
        phone: '**********',
        address: 'Test Address',
        status: 'active',
      });
    const customerId = customerRes.body.data?.customer_id;
    expect(customerId).toBeTruthy();

    // Create member
    const memberRes = await request(app)
      .post('/api/v1/members')
      .set(authHeader())
      .send({
        customer_id: customerId,
        membership_type: 'regular',
        status: 'active',
      });
    TEST_MEMBER_ID = memberRes.body.data?.member_id;
    expect(TEST_MEMBER_ID).toBeTruthy();

    // Create account
    const accountRes = await request(app)
      .post('/api/v1/accounts')
      .set(authHeader())
      .send({
        member_id: TEST_MEMBER_ID,
        account_type: 'savings',
        balance: 10000,
        status: 'active',
      });
    TEST_ACCOUNT_ID = accountRes.body.data?.account_id;
    expect(TEST_ACCOUNT_ID).toBeTruthy();
  });

  it('should create a recurring deposit', async () => {
    const res = await request(app)
      .post('/api/v1/recurring-deposits')
      .set(authHeader())
      .send({
        account_id: TEST_ACCOUNT_ID,
        member_id: TEST_MEMBER_ID,
        start_date: new Date().toISOString().split('T')[0],
        amount_per_installment: 1000,
        frequency: 'monthly',
        total_installments: 6,
        interest_rate: 6.5,
      });
    expect([200, 201]).toContain(res.statusCode);
    expect(res.body.data).toHaveProperty('id');
    RD_ID = res.body.data.id;
  });

  it('should fetch the created recurring deposit', async () => {
    const res = await request(app)
      .get(`/api/v1/recurring-deposits/${RD_ID}`)
      .set(authHeader());
    expect(res.statusCode).toBe(200);
    expect(res.body.data).toHaveProperty('id', RD_ID);
  });

  it('should list recurring deposits by member', async () => {
    const res = await request(app)
      .get(`/api/v1/recurring-deposits?member_id=${TEST_MEMBER_ID}`)
      .set(authHeader());
    expect(res.statusCode).toBe(200);
    expect(Array.isArray(res.body.data)).toBe(true);
  });

  it('should fetch payment schedule for the RD', async () => {
    // List payments for this RD (direct DB call or via endpoint if available)
    // For now, assume direct DB call is not available, so skip
    // This can be implemented if an endpoint is added
    expect(RD_ID).toBeTruthy();
  });

  it('should pay an installment', async () => {
    // Fetch payments for this RD (simulate by listing and picking the first 'due')
    // This requires a payments endpoint or direct DB access; for now, simulate
    // Assume payment_id is available (in real test, fetch from DB or add endpoint)
    // PAYMENT_ID = ...
    // const res = await request(app)
    //   .post('/api/v1/recurring-deposits/pay')
    //   .set(authHeader())
    //   .send({ payment_id: PAYMENT_ID });
    // expect(res.statusCode).toBe(200);
    expect(true).toBe(true); // Placeholder
  });

  it('should calculate penalty for missed payments', async () => {
    const res = await request(app)
      .get(`/api/v1/recurring-deposits/${RD_ID}/penalty`)
      .set(authHeader());
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('penalty');
  });

  it('should close RD early and return penalty', async () => {
    const res = await request(app)
      .post(`/api/v1/recurring-deposits/${RD_ID}/close-early`)
      .set(authHeader());
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('penalty');
  });

  it('should reject unauthenticated RD creation', async () => {
    const res = await request(app)
      .post('/api/v1/recurring-deposits')
      .send({});
    expect(res.statusCode).toBe(401);
  });
}); 